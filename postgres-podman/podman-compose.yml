version: "3.8"

services:
  postgres:
    image: postgres:15-alpine
    container_name: postgres_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: shopper_db
      POSTGRES_USER: shopper_user
      POSTGRES_PASSWORD: shopper_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - postgres_network

volumes:
  postgres_data:
    driver: local

networks:
  postgres_network:
    driver: bridge



version: "3.8"

services:
  postgres:
    image: docker.io/postgres:16
    container_name: postgres_db
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: adminpass
      POSTGRES_DB: mydb
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: always

volumes:
  postgres_data:
